﻿#pragma checksum "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "18E797976BF9FB2785FD2392DCD45945EDA30884"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// ProfessionalDriverSelectionWindow
    /// </summary>
    public partial class ProfessionalDriverSelectionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 172 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ForshanalFilter;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox KanterFilter;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HiluxFilter;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BusFilter;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PradoFilter;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ActiveFilter;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox InactiveFilter;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DriversDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/professionaldriverselectionwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 175 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ForshanalFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 202 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.ForshanalFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 202 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.ForshanalFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 3:
            this.KanterFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 205 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.KanterFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 205 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.KanterFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 4:
            this.HiluxFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 208 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.HiluxFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 208 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.HiluxFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BusFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 211 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.BusFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 211 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.BusFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PradoFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 214 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.PradoFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 214 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.PradoFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ActiveFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 224 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.ActiveFilter.Checked += new System.Windows.RoutedEventHandler(this.StatusFilter_Changed);
            
            #line default
            #line hidden
            
            #line 224 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.ActiveFilter.Unchecked += new System.Windows.RoutedEventHandler(this.StatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.InactiveFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 227 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.InactiveFilter.Checked += new System.Windows.RoutedEventHandler(this.StatusFilter_Changed);
            
            #line default
            #line hidden
            
            #line 227 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.InactiveFilter.Unchecked += new System.Windows.RoutedEventHandler(this.StatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 235 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectAllDrivers_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 243 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSelection_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 251 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 330 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportToExcel_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 338 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.DriversDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 364 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            this.DriversDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DriversDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 590 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FirstPage_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 594 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousPage_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 603 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NextPage_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 607 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LastPage_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 621 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSelection_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 632 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddNewDriver_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 643 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageContracts_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 654 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseWindow_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 15:
            
            #line 419 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Checked += new System.Windows.RoutedEventHandler(this.DriverSelection_Changed);
            
            #line default
            #line hidden
            
            #line 419 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Unchecked += new System.Windows.RoutedEventHandler(this.DriverSelection_Changed);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 477 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewContractInfo_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 504 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditDriver_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 523 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDetails_Click);
            
            #line default
            #line hidden
            break;
            case 19:
            
            #line 542 "..\..\..\..\Views\ProfessionalDriverSelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteDriver_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

