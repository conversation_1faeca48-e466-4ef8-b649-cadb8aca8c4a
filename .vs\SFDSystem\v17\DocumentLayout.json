{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\sys\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\helpers\\numbertoarabictexthelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:helpers\\numbertoarabictexthelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\views\\contracteditorwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:views\\contracteditorwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\views\\addofficerswindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:views\\addofficerswindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 419, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ContractEditorWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\ContractEditorWindow.xaml.cs", "RelativeDocumentMoniker": "Views\\ContractEditorWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\ContractEditorWindow.xaml.cs", "RelativeToolTip": "Views\\ContractEditorWindow.xaml.cs", "ViewState": "AgIAAF4BAAAAAAAAAAAuwGoBAAB5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T21:24:16.554Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "NumberToArabicTextHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Helpers\\NumberToArabicTextHelper.cs", "RelativeDocumentMoniker": "Helpers\\NumberToArabicTextHelper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Helpers\\NumberToArabicTextHelper.cs", "RelativeToolTip": "Helpers\\NumberToArabicTextHelper.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAuwKsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T21:21:41.462Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AddOfficersWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\AddOfficersWindow.xaml", "RelativeDocumentMoniker": "Views\\AddOfficersWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\AddOfficersWindow.xaml", "RelativeToolTip": "Views\\AddOfficersWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-06T21:21:30.206Z", "EditorCaption": ""}]}, {"DockedHeight": 150, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{b1e99781-ab81-11d0-b683-00aa00a3ee26}"}]}]}]}