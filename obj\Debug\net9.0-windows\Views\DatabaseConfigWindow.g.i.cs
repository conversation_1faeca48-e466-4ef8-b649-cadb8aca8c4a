﻿#pragma checksum "..\..\..\..\Views\DatabaseConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F8EDFCEB448B024192D73FE351E8F740A2EE24A7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// DatabaseConfigWindow
    /// </summary>
    public partial class DatabaseConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 69 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioServerDatabase;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioLocalDatabase;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox ServerSettingsGroup;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServerName;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDatabaseName;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioWindowsAuth;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioSqlAuth;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SqlAuthPanel;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtUsername;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox TxtPassword;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox LocalDatabaseGroup;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLocalDatabaseName;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLocalDatabasePath;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLocalDbFullPath;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox ExistingDatabasesGroup;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ListDatabases;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshDatabases;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSelectDatabase;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSaveSettings;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCreateDatabase;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTestConnection;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/databaseconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RadioServerDatabase = ((System.Windows.Controls.RadioButton)(target));
            
            #line 70 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioServerDatabase.Checked += new System.Windows.RoutedEventHandler(this.RadioServerDatabase_Checked);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RadioLocalDatabase = ((System.Windows.Controls.RadioButton)(target));
            
            #line 72 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioLocalDatabase.Checked += new System.Windows.RoutedEventHandler(this.RadioLocalDatabase_Checked);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ServerSettingsGroup = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 4:
            this.TxtServerName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtDatabaseName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.RadioWindowsAuth = ((System.Windows.Controls.RadioButton)(target));
            
            #line 118 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioWindowsAuth.Checked += new System.Windows.RoutedEventHandler(this.RadioWindowsAuth_Checked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RadioSqlAuth = ((System.Windows.Controls.RadioButton)(target));
            
            #line 120 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioSqlAuth.Checked += new System.Windows.RoutedEventHandler(this.RadioSqlAuth_Checked);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SqlAuthPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.TxtUsername = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TxtPassword = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 11:
            this.LocalDatabaseGroup = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 12:
            this.TxtLocalDatabaseName = ((System.Windows.Controls.TextBox)(target));
            
            #line 168 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.TxtLocalDatabaseName.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtLocalDatabaseName_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TxtLocalDatabasePath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TxtLocalDbFullPath = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ExistingDatabasesGroup = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 16:
            this.ListDatabases = ((System.Windows.Controls.ListBox)(target));
            return;
            case 18:
            this.BtnRefreshDatabases = ((System.Windows.Controls.Button)(target));
            
            #line 230 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnRefreshDatabases.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshDatabases_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.BtnSelectDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 235 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnSelectDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnSelectDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 246 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.BtnSaveSettings = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnSaveSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.BtnCreateDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 250 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnCreateDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnCreateDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.BtnTestConnection = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnTestConnection.Click += new System.Windows.RoutedEventHandler(this.BtnTestConnection_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 17:
            
            #line 217 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteDatabase_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

