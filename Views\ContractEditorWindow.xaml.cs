using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class ContractEditorWindow : Window
    {
        private readonly ApplicationDbContext _context;
        private ContractTemplate _contractTemplate;

        public ContractEditorWindow()
        {
            InitializeComponent();
            _context = new ApplicationDbContext();
            LoadContractTemplate();
        }

        private async void LoadContractTemplate()
        {
            try
            {
                // التأكد من وجود قاعدة البيانات والجداول
                await _context.Database.EnsureCreatedAsync();

                // البحث عن القالب الموجود أو إنشاء واحد جديد
                _contractTemplate = await _context.ContractTemplates.FirstOrDefaultAsync();

                if (_contractTemplate == null)
                {
                    // إنشاء قالب افتراضي مع البيانات المحملة مسبقاً
                    _contractTemplate = new ContractTemplate();
                    _context.ContractTemplates.Add(_contractTemplate);
                    await _context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب عقد جديد");
                }
                else
                {
                    // تحديث النص القديم إلى النص الجديد مع placeholders محدثة
                    await UpdatePriceTemplateIfNeeded();
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل قالب العقد الموجود (ID: {_contractTemplate.Id})");
                }

                // تحميل البيانات في الحقول
                LoadDataToFields();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل قالب العقد: {ex.Message}");

                // في حالة فشل الاتصال بقاعدة البيانات، استخدم البيانات الافتراضية
                _contractTemplate = new ContractTemplate();
                LoadDataToFields();

                MessageBox.Show($"تم تحميل البيانات الافتراضية. خطأ في قاعدة البيانات: {ex.Message}", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void LoadDataToFields()
        {
            try
            {
                // البحث عن عناصر TextBox في النافذة
                var contractIntroTextBox = FindName("ContractIntroductionTextBox") as System.Windows.Controls.TextBox;
                var firstPartyTextBox = FindName("FirstPartyTextBox") as System.Windows.Controls.TextBox;
                var secondPartyTextBox = FindName("SecondPartyTextBox") as System.Windows.Controls.TextBox;
                var vehicleSpecsTextBox = FindName("VehicleSpecsTextBox") as System.Windows.Controls.TextBox;
                var purposeTextBox = FindName("PurposeTextBox") as System.Windows.Controls.TextBox;
                var durationTextBox = FindName("DurationTextBox") as System.Windows.Controls.TextBox;
                var priceTextBox = FindName("PriceTextBox") as System.Windows.Controls.TextBox;
                var ownershipTextBox = FindName("OwnershipTextBox") as System.Windows.Controls.TextBox;
                var obligationsTextBox = FindName("ObligationsTextBox") as System.Windows.Controls.TextBox;

                // تحميل البيانات من القالب أو استخدام البيانات الافتراضية
                if (contractIntroTextBox != null)
                    contractIntroTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.ContractIntroduction)
                        ? _contractTemplate.ContractIntroduction : GetDefaultContractIntroduction();

                if (firstPartyTextBox != null)
                    firstPartyTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.FirstPartyTemplate)
                        ? _contractTemplate.FirstPartyTemplate : GetDefaultFirstParty();

                if (secondPartyTextBox != null)
                    secondPartyTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.SecondPartyTemplate)
                        ? _contractTemplate.SecondPartyTemplate : GetDefaultSecondParty();

                if (vehicleSpecsTextBox != null)
                    vehicleSpecsTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.VehicleSpecsTemplate)
                        ? _contractTemplate.VehicleSpecsTemplate : GetDefaultVehicleSpecs();

                if (purposeTextBox != null)
                    purposeTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.PurposeTemplate)
                        ? _contractTemplate.PurposeTemplate : GetDefaultPurpose();

                if (durationTextBox != null)
                    durationTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.DurationTemplate)
                        ? _contractTemplate.DurationTemplate : GetDefaultDuration();

                if (priceTextBox != null)
                {
                    string priceTemplate = !string.IsNullOrEmpty(_contractTemplate.PriceTemplate)
                        ? _contractTemplate.PriceTemplate : GetDefaultPrice();

                    // معالجة placeholders مع مبلغ تجريبي (رقم صحيح بدون كسور)
                    priceTextBox.Text = ProcessContractPlaceholders(priceTemplate, 115200m);
                }

                if (ownershipTextBox != null)
                    ownershipTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.OwnershipTemplate)
                        ? _contractTemplate.OwnershipTemplate : GetDefaultOwnership();

                if (obligationsTextBox != null)
                    obligationsTextBox.Text = !string.IsNullOrEmpty(_contractTemplate.ObligationsTemplate)
                        ? _contractTemplate.ObligationsTemplate : GetDefaultObligations();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        #region Default Templates
        private string GetDefaultContractIntroduction()
        {
            return @"بسم الله الرحمن الرحيم

عقد إيجار سيارة

تم إبرام هذا العقد بين كل من:";
        }

        private string GetDefaultFirstParty()
        {
            return @"الطرف الأول (مالك السيارة والسائق):
الاسم: DriverName
رقم البطاقة الشخصية: NationalId
العنوان: [العنوان]
رقم الهاتف: [رقم الهاتف]";
        }

        private string GetDefaultSecondParty()
        {
            return @"الطرف الثاني (الصندوق الاجتماعي للتنمية):
اسم المؤسسة: الصندوق الاجتماعي للتنمية
العنوان: صنعاء - الجمهورية اليمنية
ممثل بـ: VisitConductor";
        }

        private string GetDefaultVehicleSpecs()
        {
            return @"أولاً: مواصفات السيارة
نوع السيارة: VehicleType
رقم اللوحة: VehicleNumber
سنة الصنع: [سنة الصنع]
اللون: [اللون]
حالة السيارة: [جيدة/ممتازة]";
        }

        private string GetDefaultPurpose()
        {
            return @"ثانياً: غرض الانتفاع
تستخدم السيارة لأغراض الزيارات الميدانية والمهام الرسمية للصندوق الاجتماعي للتنمية وفقاً لبرنامج العمل المحدد.";
        }

        private string GetDefaultDuration()
        {
            return @"ثالثاً: المدة الإيجارية
مدة الإيجار: DaysCount أيام
تاريخ البداية: StartDate
تاريخ النهاية: EndDate";
        }

        private string GetDefaultPrice()
        {
            return @"رابعاً: القيمة الإيجارية (الإجراء):
اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بكاملها مبلغ وقدره ({TotalPriceFormatted}) {TotalPriceArabic}، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف حسب عدد الأيام الفعلية المنجزة.";
        }

        private string GetDefaultOwnership()
        {
            return @"خامساً: إقرار الملكية
يقر الطرف الأول بأنه المالك الشرعي للسيارة المذكورة أعلاه وأن له الحق الكامل في تأجيرها.";
        }

        private string GetDefaultObligations()
        {
            return @"سادساً: التزامات الطرف الأول
1. تسليم السيارة في حالة جيدة وصالحة للاستخدام
2. توفير السائق المؤهل والمرخص
3. تحمل تكاليف الوقود والصيانة العادية
4. الالتزام بالمواعيد المحددة
5. احترام قوانين المرور والسلامة
6. عدم استخدام السيارة لأغراض أخرى غير المتفق عليها";
        }
        #endregion

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء عملية الحفظ...");

                // التأكد من وجود القالب
                if (_contractTemplate == null)
                {
                    _contractTemplate = new ContractTemplate();
                    _context.ContractTemplates.Add(_contractTemplate);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب جديد");
                }

                // البحث عن عناصر TextBox في النافذة وحفظ البيانات
                var contractIntroTextBox = FindName("ContractIntroductionTextBox") as System.Windows.Controls.TextBox;
                var firstPartyTextBox = FindName("FirstPartyTextBox") as System.Windows.Controls.TextBox;
                var secondPartyTextBox = FindName("SecondPartyTextBox") as System.Windows.Controls.TextBox;
                var vehicleSpecsTextBox = FindName("VehicleSpecsTextBox") as System.Windows.Controls.TextBox;
                var purposeTextBox = FindName("PurposeTextBox") as System.Windows.Controls.TextBox;
                var durationTextBox = FindName("DurationTextBox") as System.Windows.Controls.TextBox;
                var priceTextBox = FindName("PriceTextBox") as System.Windows.Controls.TextBox;
                var ownershipTextBox = FindName("OwnershipTextBox") as System.Windows.Controls.TextBox;
                var obligationsTextBox = FindName("ObligationsTextBox") as System.Windows.Controls.TextBox;

                // حفظ البيانات من الحقول مع التحقق من وجود النص
                var oldIntro = _contractTemplate.ContractIntroduction;
                var newIntro = contractIntroTextBox?.Text ?? "";

                System.Diagnostics.Debug.WriteLine($"🔍 مقارنة النصوص:");
                System.Diagnostics.Debug.WriteLine($"   القديم: '{oldIntro}'");
                System.Diagnostics.Debug.WriteLine($"   الجديد: '{newIntro}'");
                System.Diagnostics.Debug.WriteLine($"   هل تغير؟ {oldIntro != newIntro}");

                _contractTemplate.ContractIntroduction = newIntro;
                _contractTemplate.FirstPartyTemplate = firstPartyTextBox?.Text ?? "";
                _contractTemplate.SecondPartyTemplate = secondPartyTextBox?.Text ?? "";
                _contractTemplate.VehicleSpecsTemplate = vehicleSpecsTextBox?.Text ?? "";
                _contractTemplate.PurposeTemplate = purposeTextBox?.Text ?? "";
                _contractTemplate.DurationTemplate = durationTextBox?.Text ?? "";
                _contractTemplate.PriceTemplate = priceTextBox?.Text ?? "";
                _contractTemplate.OwnershipTemplate = ownershipTextBox?.Text ?? "";
                _contractTemplate.ObligationsTemplate = obligationsTextBox?.Text ?? "";
                _contractTemplate.LastModified = DateTime.Now;

                // إذا كان قالب جديد، تعيين تاريخ الإنشاء
                if (_contractTemplate.Id == 0)
                {
                    _contractTemplate.CreatedDate = DateTime.Now;
                }

                System.Diagnostics.Debug.WriteLine($"📝 النص القديم: {oldIntro?.Substring(0, Math.Min(50, oldIntro?.Length ?? 0))}...");
                System.Diagnostics.Debug.WriteLine($"📝 النص الجديد: {_contractTemplate.ContractIntroduction?.Substring(0, Math.Min(50, _contractTemplate.ContractIntroduction?.Length ?? 0))}...");

                // تحديث الكائن في السياق إذا كان موجوداً
                if (_contractTemplate.Id > 0)
                {
                    _context.ContractTemplates.Update(_contractTemplate);
                    System.Diagnostics.Debug.WriteLine($"🔄 تحديث القالب الموجود (ID: {_contractTemplate.Id})");
                }

                // إجبار Entity Framework على تتبع التغييرات
                _context.Entry(_contractTemplate).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                // حفظ التغييرات
                var result = await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine($"💾 نتيجة الحفظ: {result} تغيير");

                if (result > 0)
                {
                    MessageBox.Show("✅ تم حفظ التغييرات بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // إعادة تحميل البيانات من قاعدة البيانات للتأكد
                    await _context.Entry(_contractTemplate).ReloadAsync();
                }
                else
                {
                    MessageBox.Show("⚠️ لم يتم حفظ أي تغييرات - ربما لم تقم بتعديل أي شيء", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحفظ: {ex.Message}");
                MessageBox.Show($"❌ خطأ في حفظ التغييرات: {ex.Message}\n\nتفاصيل الخطأ: {ex.InnerException?.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من استعادة النصوص الافتراضية؟ سيتم فقدان جميع التعديلات الحالية.", 
                "تأكيد الاستعادة", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // استعادة النصوص الافتراضية
                _contractTemplate = new ContractTemplate();
                LoadDataToFields();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }

        /// <summary>
        /// تحديث نص القيمة الإيجارية إذا كان يحتوي على النص القديم
        /// </summary>
        private async Task UpdatePriceTemplateIfNeeded()
        {
            try
            {
                if (_contractTemplate != null)
                {
                    // فرض تحديث النص إلى النسخة الجديدة دائماً
                    string newTemplate = GetDefaultPrice();

                    if (_contractTemplate.PriceTemplate != newTemplate)
                    {
                        _contractTemplate.PriceTemplate = newTemplate;
                        _contractTemplate.LastModified = DateTime.Now;

                        _context.ContractTemplates.Update(_contractTemplate);
                        await _context.SaveChangesAsync();

                        System.Diagnostics.Debug.WriteLine("✅ تم تحديث نص القيمة الإيجارية إلى النسخة الجديدة");
                        System.Diagnostics.Debug.WriteLine($"النص الجديد: {newTemplate}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث نص القيمة الإيجارية: {ex.Message}");
            }
        }

        /// <summary>
        /// معالجة placeholders في نص العقد مع بيانات فعلية
        /// </summary>
        /// <param name="template">النص الأصلي</param>
        /// <param name="amount">المبلغ</param>
        /// <returns>النص بعد استبدال placeholders</returns>
        private string ProcessContractPlaceholders(string template, decimal amount)
        {
            try
            {
                string processedText = template;

                // تنسيق المبلغ رقمياً مع الفواصل والكسور العشرية
                string formattedAmount = DriverManagementSystem.Helpers.NumberToArabicTextHelper.FormatAmountWithDecimals(amount);

                // تحويل المبلغ إلى نص عربي
                string arabicAmount = DriverManagementSystem.Helpers.NumberToArabicTextHelper.ConvertAmountToArabicText(amount);

                // استبدال placeholders
                processedText = processedText.Replace("{TotalPriceFormatted}", formattedAmount);
                processedText = processedText.Replace("{TotalPriceArabic}", arabicAmount);
                processedText = processedText.Replace("{TotalPrice}", formattedAmount); // للتوافق مع النسخة القديمة

                return processedText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة placeholders: {ex.Message}");
                return template;
            }
        }
    }
}
