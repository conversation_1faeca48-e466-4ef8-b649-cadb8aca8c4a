<Window x:Class="DriverManagementSystem.Views.SimpleAssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="842"
        Width="595"
        MinHeight="800"
        MinWidth="580"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
>

    <Grid Margin="0,0,0,-888">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Toolbar -->
        <Border Grid.Row="0" Background="#34495E" Padding="10,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="📋 التكليف الرسمي - الزيارات الميدانية"
                         FontSize="16" FontWeight="Bold" Foreground="White"
                         VerticalAlignment="Center" Margin="10,0"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="❌ إغلاق" Click="CloseButton_Click"
                            Background="#E74C3C" Foreground="White" Padding="12,6"
                            FontWeight="Bold" FontSize="12" BorderThickness="0"
                            Cursor="Hand"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled"
                      Background="White" Padding="0">
            <Border Background="White" Padding="20,15" BorderBrush="#E1E8ED" BorderThickness="1">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3"
                                      Opacity="0.2" BlurRadius="8"/>
                </Border.Effect>
                <StackPanel>

                    <!-- Official Header -->
                    <Border Background="White" BorderBrush="#2C3E50" BorderThickness="0,0,0,2"
                            Padding="15,20" Margin="0,0,0,20">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2"
                                              Opacity="0.15" BlurRadius="5"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="80"/>
                            </Grid.ColumnDefinitions>

                            <!-- الشعار على اليسار -->
                            <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1"
                                    Padding="5" Background="White" CornerRadius="3">
                                <Image Source="pack://application:,,,/Icons/sfd.png"
                                       Width="50" Height="50" Stretch="Uniform"/>
                            </Border>

                            <!-- النصوص في الوسط -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="الجمهورية اليمنية" FontSize="18" FontWeight="Bold"
                                         Foreground="#2C3E50" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="رئاسة مجلس الوزراء" FontSize="16" FontWeight="SemiBold"
                                         Foreground="#34495E" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                                <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="16" FontWeight="SemiBold"
                                         Foreground="#34495E" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                                <TextBlock Text="فرع ذمار والبيضاء" FontSize="14" FontWeight="Medium"
                                         Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- مساحة فارغة على اليمين للتوازن -->
                            <Border Grid.Column="2"/>
                        </Grid>
                    </Border>

                    <!-- عنوان التكليف -->
                    <Border Background="#1e3c72" CornerRadius="5" Padding="15,12" Margin="0,0,0,20">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3"
                                              Opacity="0.25" BlurRadius="6"/>
                        </Border.Effect>
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📋 تكليف رسمي 📋" FontSize="18" FontWeight="Bold"
                                     Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding AssignmentNumber}" FontSize="12"
                                     Foreground="#E8F4FD" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- معلومات الزيارة -->
                    <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                            CornerRadius="5" Padding="15,12" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2"
                                              Opacity="0.15" BlurRadius="4"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📋 معلومات المهمة" FontSize="14" FontWeight="Bold"
                                     Foreground="#2C3E50" Margin="0,0,0,10"/>

                            <StackPanel Grid.Row="1" Margin="0,0,0,8">
                                <TextBlock Text="🏗️ المشروع:" FontWeight="Bold" FontSize="12" Margin="0,0,0,3"/>
                                <TextBlock Text="مشروع تطوير المجتمعات الريفية" FontSize="11" TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Margin="0,0,0,8">
                                <TextBlock Text="⚡ النشاط:" FontWeight="Bold" FontSize="12" Margin="0,0,0,3"/>
                                <TextBlock Text="{Binding MissionPurpose}" FontSize="11" TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3">
                                <TextBlock Text="📅 تاريخ التحرك:" FontWeight="Bold" FontSize="12" Margin="0,0,0,3"/>
                                <TextBlock FontSize="11">
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="من {0} إلى {1} ({2})">
                                            <Binding Path="DepartureDate"/>
                                            <Binding Path="ReturnDate"/>
                                            <Binding Path="DurationText"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- القائمون بالزيارة -->
                    <Border Background="White" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="5" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3"
                                              Opacity="0.2" BlurRadius="5"/>
                        </Border.Effect>
                        <StackPanel>
                            <Border Background="#1e3c72" CornerRadius="4,4,0,0" Padding="10,8">
                                <TextBlock Text="👥 القائمون بالزيارة" FontSize="13" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <ItemsControl ItemsSource="{Binding Visitors}" Margin="10,8">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                                                CornerRadius="3" Padding="10,8" Margin="0,0,0,6">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="25"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="80"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0" Background="#1e3c72" CornerRadius="12"
                                                        Width="20" Height="20">
                                                    <TextBlock Text="{Binding Index}" FontSize="10" FontWeight="Bold"
                                                             Foreground="White" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"/>
                                                </Border>

                                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                    <TextBlock Text="{Binding OfficerName}" FontSize="11" FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding OfficerRank}" FontSize="9" Foreground="#666"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2">
                                                    <TextBlock Text="📞 الهاتف" FontSize="8" Foreground="#666"/>
                                                    <TextBlock Text="{Binding PhoneNumber}" FontSize="10"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="3">
                                                    <TextBlock Text="🆔 البطاقة" FontSize="8" Foreground="#666"/>
                                                    <TextBlock Text="{Binding OfficerCode}" FontSize="10"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- السائق -->
                    <Border Background="White" BorderBrush="#FF9800" BorderThickness="2"
                            CornerRadius="8" Margin="0,0,0,20">
                        <StackPanel>
                            <Border Background="#FF9800" CornerRadius="6,6,0,0" Padding="15">
                                <TextBlock Text="🚗 السائق المكلف" FontSize="16" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <Border Background="#FFF3E0" Padding="15" Margin="15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="120"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="👤 اسم السائق" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="{Binding DriverName}" FontSize="14" FontWeight="Bold"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="📞 الهاتف" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="{Binding DriverPhoneNumber}" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="🚙 نوع السيارة" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="{Binding VehicleType}" FontSize="12" FontWeight="Bold"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- النص الختامي -->
                    <Border Background="#F8F9FA" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="وعليه تكرموا بالتعاون مع المذكورين أعلاه لما فيه المصلحة العامة وتحقيق أهداف المؤسسة."
                                     FontSize="14" TextAlignment="Justify" TextWrapping="Wrap" Margin="0,0,0,10"/>
                            <TextBlock Text="وشكراً لحسن تعاونكم" FontSize="14" FontWeight="Bold"
                                     Foreground="#1e3c72" HorizontalAlignment="Right"/>
                        </StackPanel>
                    </Border>

                    <!-- التوقيع -->
                    <Border Background="White" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="8" Padding="20" HorizontalAlignment="Center" Margin="0,0,0,40">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="✍️ التوقيع" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Text="مدير الفرع" FontSize="14" FontWeight="Bold"
                                     Foreground="#1e3c72" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                            <Rectangle Height="1" Width="120" Fill="#1e3c72" Margin="0,10,0,10"/>
                            <TextBlock Text="م/محمد محمد الديلمي" FontSize="12"
                                     Foreground="#2a5298" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Official Footer -->
                    <Border Background="White" BorderBrush="#2C3E50" BorderThickness="0,3,0,0"
                            Padding="40,30" Margin="0,40,0,0">
                        <TextBlock Text="الجمهورية اليمنية" FontSize="22" FontWeight="Bold"
                                 Foreground="#2C3E50" HorizontalAlignment="Center"/>
                    </Border>

                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#95A5A6" Padding="10,6">
            <TextBlock Text="📄 جاهز للطباعة - مقاس A4"
                     FontSize="11" Foreground="White" VerticalAlignment="Center"
                     HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
